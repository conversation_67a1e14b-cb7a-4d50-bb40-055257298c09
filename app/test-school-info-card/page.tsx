'use client';

import React from 'react';
import { SchoolInfoCard } from '@/components/molecules/SchoolInfoCard/SchoolInfoCard';
import { ISchoolResponse } from '@/apis/schoolApi';

// Test page for SchoolInfoCard component
export default function TestSchoolInfoCardPage() {
  // Example school data with all fields populated
  const fullSchoolData: ISchoolResponse = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Springfield Elementary School',
    address: '123 Main Street, Springfield, IL 62701',
    phoneNumber: '+****************',
    registeredNumber: 'REG-2024-001',
    email: '<EMAIL>',
    admin: {
      id: 'admin-123',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'admin',
    },
    brand: {
      id: 'brand-123',
      logo: 'https://example.com/logo.png',
      color: '#3872fa',
      image: 'https://example.com/image.png',
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  // Example school data with minimal fields
  const minimalSchoolData: ISchoolResponse = {
    id: '456e7890-e89b-12d3-a456-426614174001',
    name: 'Minimal School',
    address: '',
    phoneNumber: '',
    registeredNumber: '',
    email: '',
  };

  // Example school data with some optional fields
  const partialSchoolData: ISchoolResponse = {
    id: '789e0123-e89b-12d3-a456-426614174002',
    name: 'Partial Data School',
    address: '456 Oak Avenue, Somewhere, CA 90210',
    phoneNumber: '',
    registeredNumber: 'REG-2024-002',
    email: '<EMAIL>',
  };

  const handleEdit = (schoolId: string) => {
    console.log('Edit school with ID:', schoolId);
    alert(`Edit school with ID: ${schoolId}`);
  };

  return (
    <div className="min-h-screen bg-base-200 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            SchoolInfoCard Component Test
          </h1>
          <p className="text-gray-600">
            Testing the SchoolInfoCard molecule component with different data scenarios.
          </p>
        </div>

        <div className="space-y-12">
          {/* Full School Data Example */}
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Full School Data
            </h2>
            <p className="text-gray-600 mb-4">
              School with all fields populated including admin information.
            </p>
            <div className="max-w-md">
              <SchoolInfoCard
                school={fullSchoolData}
                onEdit={handleEdit}
              />
            </div>
          </section>

          {/* Minimal School Data Example */}
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Minimal School Data
            </h2>
            <p className="text-gray-600 mb-4">
              School with only required fields (name and id). Optional fields should not be displayed.
            </p>
            <div className="max-w-md">
              <SchoolInfoCard
                school={minimalSchoolData}
                onEdit={handleEdit}
              />
            </div>
          </section>

          {/* Partial School Data Example */}
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Partial School Data
            </h2>
            <p className="text-gray-600 mb-4">
              School with some optional fields populated. Only populated fields should be displayed.
            </p>
            <div className="max-w-md">
              <SchoolInfoCard
                school={partialSchoolData}
                onEdit={handleEdit}
              />
            </div>
          </section>

          {/* Custom Styling Example */}
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Custom Styling Example
            </h2>
            <p className="text-gray-600 mb-4">
              School card with custom border and shadow styling.
            </p>
            <div className="max-w-md">
              <SchoolInfoCard
                school={fullSchoolData}
                onEdit={handleEdit}
                className="border-2 border-primary shadow-lg"
              />
            </div>
          </section>

          {/* Responsive Grid Example */}
          <section>
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Responsive Grid Layout
            </h2>
            <p className="text-gray-600 mb-4">
              Multiple school cards in a responsive grid layout.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <SchoolInfoCard
                school={fullSchoolData}
                onEdit={handleEdit}
              />
              <SchoolInfoCard
                school={partialSchoolData}
                onEdit={handleEdit}
              />
              <SchoolInfoCard
                school={minimalSchoolData}
                onEdit={handleEdit}
              />
            </div>
          </section>
        </div>

        {/* Instructions */}
        <div className="mt-12 p-6 bg-info/10 rounded-lg border border-info/20">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Testing Instructions
          </h3>
          <ul className="list-disc list-inside space-y-1 text-gray-600">
            <li>Click the "Edit" buttons to test the onEdit callback functionality</li>
            <li>Resize the browser window to test responsive behavior</li>
            <li>Check that optional fields are only displayed when they have values</li>
            <li>Verify that the DaisyUI styling is applied correctly</li>
            <li>Test keyboard navigation to ensure accessibility</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
